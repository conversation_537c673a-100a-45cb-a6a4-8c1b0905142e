<script lang="ts">
	// import * as Form from '$lib/components/ui/form';
	// import { Input } from '$lib/components/ui/input';
	// import * as Alert from '$lib/components/ui/alert';

	import { formSchema, type FormSchema, type Response } from './schema';
	import SuperDebug, { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { Button, Label, Input, Card, Toggle } from 'flowbite-svelte';
	import { zodClient } from 'sveltekit-superforms/adapters';

	import Fa from 'svelte-fa';
	import { faUser, faKey, faCircleExclamation } from '@fortawesome/free-solid-svg-icons';
	import { Field, Control, FieldErrors } from 'formsnap';
	import { Alert, Spinner } from 'flowbite-svelte';

	export let data: SuperValidated<Infer<FormSchema>>;
	export let response: Response;

	const form = superForm(data, {
		validators: zodClient(formSchema)
	});

	const { form: formData, enhance, delayed } = form;
</script>

<!-- <SuperDebug data={response} /> -->
<!-- {#if response?.form?.message?.status == 'fail'}
	<Alert border>
		<div class="flex items-center gap-3">
			<Fa icon={faCircleExclamation} />
			<span class="text-lg font-medium">Login failed</span>
		</div>
		<p class="mt-2 text-sm">
			{response?.form?.message?.detail}
		</p>
	</Alert>
{/if} -->
<!-- Main Container -->
<!-- <br /> -->
 
<div class="row text-center">
	<!-- Left Side - Image -->
	<!-- <div class="column">
        <img src="/images/Salmate-Logo-Transparent.png" alt="Salmate Logo" class="w-64 h-64 object-contain">
    </div> -->

	<!-- Right Side - Login -->
	<div class="column">
		<form class="flex flex-col" action="?/login" method="POST" use:enhance>
			<h3 class="text-left text-5xl font-bold text-gray-900 dark:text-white mb-5">Login</h3>
			<br />
			<Field {form} name="username">
				<Control let:attrs>
					<Label class="text-left">Username</Label>
					<div class="relative mb-2">
						<div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
							<Fa icon={faUser} class="text-gray-500" />
						</div>
						<Input {...attrs} type="text" bind:value={$formData.username} placeholder="Enter your username" class="ps-10 focus:ring-2 focus:ring-blue focus:border-transparent" required />
						<div class="absolute -bottom-4 left-0 text-red-500 text-xs">
							<FieldErrors />
						</div>
					</div>
				</Control>
			</Field>
			<br />
			<Field {form} name="password">
				<Control let:attrs>
					<Label class="text-left">Password</Label>
					<div class="relative mb-6">
						<div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
							<Fa icon={faKey} class="text-gray-500" />
						</div>
						<Input {...attrs} type="password" bind:value={$formData.password} placeholder="Enter your password" class="ps-10 focus:ring-2 focus:ring-blue focus:border-transparent" required />
						<div class="absolute -bottom-4 left-0 text-red-500 text-xs">
							<FieldErrors />
						</div>
					</div>
				</Control>
			</Field>
			<br />
			<div class="flex flex-col gap-0">
				<Button type="submit" class="w-full bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600">
					{#if $delayed}
						<Spinner class="me-3" size="4" color="white" /> Logging In
					{:else}
						Login
					{/if}
				</Button>
				{#if response?.form?.message?.status == 'fail'}
                    <div class="text-red-500 text-medium">
                        {response?.form?.message?.detail || 'Invalid username or password'}
                    </div>
				{/if}
			</div>

			<!-- Link back to CRM welcome page -->
			<div class="mt-4 text-center">
				<p class="text-sm text-gray-600">
					New to Salmate?
					<a href="/crm" class="text-cyan-600 hover:text-cyan-700 font-medium">
						Learn more about our CRM solution
					</a>
				</p>
			</div>
		</form>
	</div>
</div>
