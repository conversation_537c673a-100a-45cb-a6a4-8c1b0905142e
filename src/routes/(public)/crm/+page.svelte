<script lang="ts">
    import { <PERSON><PERSON>, <PERSON>, Badge } from 'flowbite-svelte';
    import { 
        UserGroupSolid, 
        TicketSolid, 
        ChartBarSolid,
        ArrowRightOutline,
        CheckCircleSolid
    } from 'flowbite-svelte-icons';
    import type { PageData } from './$types';
    
    export let data: PageData;
    
    let pageTitle = "Welcome to Salmate CRM";
    
    // Feature highlights
    const features = [
        {
            icon: TicketSolid,
            title: "Ticket Management",
            description: "Efficiently manage and track customer support tickets with our intuitive system."
        },
        {
            icon: UserGroupSolid,
            title: "Customer Management",
            description: "Organize and maintain comprehensive customer profiles and interaction history."
        },
        {
            icon: ChartBarSolid,
            title: "Analytics & Reporting",
            description: "Get insights into your customer service performance with detailed analytics."
        }
    ];
    
    // Benefits list
    const benefits = [
        "Streamlined customer support workflow",
        "Real-time ticket tracking and updates",
        "Comprehensive customer database",
        "Advanced reporting and analytics",
        "Team collaboration tools",
        "Knowledge base management"
    ];
</script>

<svelte:head>
    <title>{pageTitle}</title>
    <meta name="description" content="Welcome to Salmate CRM - Your comprehensive customer relationship management solution for efficient support and customer management." />
</svelte:head>

<!-- Hero Section -->
<div class="bg-gradient-to-r from-cyan-400 to-sky-500 text-white">
    <div class="container mx-auto px-4 py-12 md:py-16 text-center">
        <h1 class="text-3xl sm:text-4xl md:text-6xl font-bold mb-4 md:mb-6">
            Welcome to <span class="text-yellow-300">Salmate</span>
        </h1>
        <p class="text-lg sm:text-xl md:text-2xl mb-6 md:mb-8 max-w-3xl mx-auto px-2">
            Your comprehensive Customer Relationship Management solution for efficient support,
            streamlined workflows, and exceptional customer experiences.
        </p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center px-4">
            {#if data.isLogin}
                <Button href="/" size="lg" class="bg-white text-cyan-600 hover:bg-gray-100 w-full sm:w-auto">
                    Go to Dashboard
                    <ArrowRightOutline class="w-5 h-5 ml-2" />
                </Button>
            {:else}
                <Button href="/login" size="lg" class="bg-white text-cyan-600 hover:bg-gray-100 w-full sm:w-auto">
                    Get Started
                    <ArrowRightOutline class="w-5 h-5 ml-2" />
                </Button>
            {/if}
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Powerful Features for Your Business
            </h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover how Salmate can transform your customer service operations with our comprehensive suite of tools.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            {#each features as feature}
                <Card class="text-center p-6 hover:shadow-lg transition-shadow">
                    <div class="flex justify-center mb-4">
                        <div class="p-3 bg-cyan-100 rounded-full">
                            <svelte:component this={feature.icon} class="w-8 h-8 text-cyan-600" />
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                    <p class="text-gray-600">{feature.description}</p>
                </Card>
            {/each}
        </div>
    </div>
</div>

<!-- Benefits Section -->
<div class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Why Choose Salmate?
                </h2>
                <p class="text-lg text-gray-600 mb-8">
                    Built for modern businesses, Salmate provides everything you need to deliver 
                    exceptional customer service and build lasting relationships.
                </p>
                
                <div class="space-y-4">
                    {#each benefits as benefit}
                        <div class="flex items-center">
                            <CheckCircleSolid class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                            <span class="text-gray-700">{benefit}</span>
                        </div>
                    {/each}
                </div>
            </div>
            
            <div class="lg:text-center">
                <Card class="p-8 bg-gradient-to-br from-cyan-50 to-sky-50 border-cyan-200">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready to Get Started?</h3>
                    <p class="text-gray-600 mb-6">
                        Join thousands of businesses already using Salmate to improve their customer service operations.
                    </p>
                    
                    {#if data.isLogin}
                        <Button href="/" class="w-full bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600">
                            Access Your Dashboard
                        </Button>
                    {:else}
                        <Button href="/login" class="w-full bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600">
                            Sign In to Continue
                        </Button>
                    {/if}
                </Card>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="py-16 bg-gray-900 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            Transform Your Customer Service Today
        </h2>
        <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Experience the power of streamlined customer management with Salmate's comprehensive CRM solution.
        </p>
        
        {#if !data.isLogin}
            <Button href="/login" size="lg" class="bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600">
                Get Started Now
                <ArrowRightOutline class="w-5 h-5 ml-2" />
            </Button>
        {/if}
    </div>
</div>
