<script lang="ts">
    import 'tailwindcss/tailwind.css';
    import Footer from '$lib/components/footer.svelte';
    import { Button } from 'flowbite-svelte';
    import type { LayoutData } from './$types';

    export let data: LayoutData;
</script>

<!-- Public layout without authentication requirements -->
<div class="min-h-screen bg-gray-100 flex flex-col">
    <!-- Simple Header for Public Pages -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="container mx-auto px-4 py-3 md:py-4 flex justify-between items-center">
            <div class="flex items-center">
                <h1 class="text-xl md:text-2xl font-bold text-gray-900">
                    <a href="/crm" class="hover:text-cyan-600 transition-colors">Salmate</a>
                </h1>
            </div>

            <nav class="flex items-center space-x-2 md:space-x-4">
                <a href="/crm" class="hidden sm:block text-gray-600 hover:text-cyan-600 transition-colors">Home</a>
                {#if data.isLogin}
                    <Button href="/" size="sm" class="bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600 text-sm">
                        Dashboard
                    </Button>
                {:else}
                    <Button href="/login" size="sm" class="bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600 text-sm">
                        Login
                    </Button>
                {/if}
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <div class="flex-1">
        <slot />
    </div>

    <!-- Footer -->
    <Footer />
</div>
